"use client"

import React from "react"

// Mall Badge - sesuai dengan Velozio
export const SellzioMallBadge = () => (
  <div className="inline-flex items-center rounded-[3px] overflow-hidden shadow-sm text-[10px] font-bold h-3.5 mr-1 mb-1 align-middle">
    <div className="bg-[#cc0000] text-white px-1 h-full flex items-center">Mall</div>
    <div className="h-full w-px bg-white/50"></div>
    <div className="bg-[#cc0000] text-white px-1 h-full flex items-center">ORI</div>
  </div>
)

// Star Badge - format seperti Mall | ORI dengan divider
export const SellzioStarBadge = ({ hasPlus = true }: { hasPlus?: boolean }) => (
  <div className="inline-flex items-center rounded-[3px] overflow-hidden shadow-sm text-[10px] font-bold h-3.5 mr-1 mb-1 align-middle">
    <div className="bg-[#ee4d2d] text-white px-1 h-full flex items-center">Star</div>
    {hasPlus && (
      <>
        <div className="h-full w-px bg-white/50"></div>
        <div className="bg-[#fff0e5] text-[#ee4d2d] px-1 h-full flex items-center">Plus</div>
      </>
    )}
  </div>
)

// Star Lite Badge - format seperti Mall | ORI dengan divider
export const SellzioStarLiteBadge = () => (
  <div className="inline-flex items-center rounded-[3px] overflow-hidden shadow-sm text-[10px] font-bold h-3.5 mr-1 mb-1 align-middle">
    <div className="bg-[#ee4d2d] text-white px-1 h-full flex items-center">Star</div>
    <div className="h-full w-px bg-white/50"></div>
    <div className="bg-[#fff0e5] text-[#ee4d2d] px-1 h-full flex items-center">Lite</div>
  </div>
)

// COD Badge - sesuai dengan Velozio (di pojok kanan atas)
export const SellzioCodBadge = () => (
  <div className="absolute top-0 right-0 bg-[#ee4d2d] text-white text-[10px] font-bold px-1 py-0.5 rounded-bl-[3px] z-10">
    COD
  </div>
)

// Discount Badge - sesuai dengan Velozio (di pojok kanan atas)
export const SellzioDiscountBadge = ({ discount, isFlashSale = false }: { discount: string; isFlashSale?: boolean }) => (
  <div className="absolute top-0 right-0 bg-[#ee4d2d] text-white text-[10px] font-bold px-1 py-0.5 rounded-bl-[3px] z-10 flex items-center">
    {isFlashSale && (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-2.5 h-2.5 mr-0.5">
        <path fillRule="evenodd" d="M14.615 1.595a.75.75 0 01.359.852L12.982 9.75h7.268a.75.75 0 01.548 1.262l-10.5 11.25a.75.75 0 01-1.272-.71L10.018 14.25H2.75a.75.75 0 01-.548-1.262l10.5-11.25a.75.75 0 01.913-.143z" clipRule="evenodd" />
      </svg>
    )}
    -{discount}
  </div>
)

// Rating Stars - satu bintang dengan fill sesuai rating
export const SellzioRatingStars = ({ rating = 0 }: { rating?: number }) => {
  const safeRating = typeof rating === "number" && !isNaN(rating) ? rating : 0
  const fillPercentage = Math.min((safeRating / 5) * 100, 100)

  // Debug log
  console.log('SellzioRatingStars rendered:', { rating, safeRating, fillPercentage })

  return (
    <div className="inline-flex items-center bg-yellow-100 border border-yellow-300 rounded px-1.5 py-0.5 mt-2 mb-1.5 w-fit" style={{ display: 'flex', visibility: 'visible' }}>
      <div className="relative mr-1">
        {/* Background star (gray) */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-3 h-3 text-gray-300"
        >
          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
        </svg>

        {/* Filled star (red) with percentage fill */}
        <div
          className="absolute inset-0 overflow-hidden"
          style={{ width: `${fillPercentage}%` }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-3 h-3 text-red-500"
          >
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
          </svg>
        </div>
      </div>
      <span className="text-gray-700 font-medium text-[11px]">{safeRating.toFixed(1)}</span>
    </div>
  )
}

// Shipping Badge - dengan waktu pengiriman dan nama kota
export const SellzioShippingBadge = ({ type = "instan", city = "Jakarta" }: { type?: string; city?: string }) => {
  const getShippingDetails = () => {
    switch (type.toLowerCase()) {
      case "instan":
        return { color: "#00bfa5", days: "1-2 hari" }
      case "gratis":
        return { color: "#ee4d2d", days: "2-3 hari" }
      case "reguler":
        return { color: "#757575", days: "3-5 hari" }
      default:
        return { color: "#00bfa5", days: "1-2 hari" }
    }
  }

  const { color, days } = getShippingDetails()

  return (
    <div className="flex items-center text-[10px] text-[#666] mt-1">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 640 512"
        className="w-3 h-3 mr-1"
        fill="currentColor"
        style={{ color }}
      >
        <path d="M48 0C21.5 0 0 21.5 0 48V368c0 26.5 21.5 48 48 48H64c0 53 43 96 96 96s96-43 96-96H384c0 53 43 96 96 96s96-43 96-96h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V288 256 237.3c0-17-6.7-33.3-18.7-45.3L512 114.7c-12-12-28.3-18.7-45.3-18.7H416V48c0-26.5-21.5-48-48-48H48zM416 160h50.7L544 237.3V256H416V160zM112 416a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm368-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z" />
      </svg>
      <span>{days}</span>
      <span className="mx-1 text-gray-400">|</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="currentColor"
        className="w-2.5 h-2.5 mr-0.5 text-green-500"
      >
        <path fillRule="evenodd" d="m11.54 22.351.07.04.028.016a.76.76 0 0 0 .723 0l.028-.015.071-.041a16.975 16.975 0 0 0 1.144-.742 19.58 19.58 0 0 0 2.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 0 0-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 0 0 2.682 2.282 16.975 16.975 0 0 0 1.145.742ZM12 13.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" clipRule="evenodd" />
      </svg>
      <span className="truncate max-w-[80px]">{city}</span>
    </div>
  )
}

// Terlaris Badge - sesuai dengan Velozio
export const SellzioTerlarisBadge = () => (
  <div className="inline-flex items-center bg-white text-[#ee4d2d] text-[10px] font-semibold py-0.5 px-1.5 rounded-sm border border-[#ee4d2d] border-l-[3px] mb-1.5">
    <span className="flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 mr-0.5">
        <path
          fillRule="evenodd"
          d="M5.166 2.621v.858c-1.035.148-2.059.33-3.071.543a.75.75 0 00-.584.859 6.753 6.753 0 006.138 5.6 6.73 6.73 0 002.743 1.346A6.707 6.707 0 019.279 15H8.54c-1.036 0-1.875.84-1.875 1.875V19.5h-.75a2.25 2.25 0 00-2.25 2.25c0 .414.336.75.75.75h15a.75.75 0 00.75-.75 2.25 2.25 0 00-2.25-2.25h-.75v-2.625c0-1.036-.84-1.875-1.875-1.875h-.739a6.706 6.706 0 01-1.112-3.173 6.73 6.73 0 002.743-1.347 6.753 6.753 0 006.139-********* 0 00-.585-.858 47.077 47.077 0 00-3.07-.543V2.62a.75.75 0 00-.658-.744 49.22 49.22 0 00-6.093-.377c-2.063 0-4.096.128-6.093.377a.75.75 0 00-.657.744zm0 2.629c0 1.196.312 2.32.857 3.294A5.266 5.266 0 013.16 5.337a45.6 45.6 0 012.006-.343v.256zm13.5 0v-.256c.674.1 1.343.214 2.006.343a5.265 5.265 0 01-2.863 3.207 6.72 6.72 0 00.857-3.294z"
          clipRule="evenodd"
        />
      </svg>
      Terlaris
    </span>
  </div>
)

// Live Badge - sesuai dengan Velozio (inline badge)
export const SellzioLiveBadge = () => (
  <div className="inline-flex items-center bg-[#ee4d2d] text-white text-[8px] font-semibold px-1 h-3.5 rounded-[3px] mr-1 mb-1 align-middle">
    <span className="mr-1 inline-flex items-end gap-px h-1.5">
      <span className="w-px bg-white sellzio-bar-1"></span>
      <span className="w-px bg-white sellzio-bar-2"></span>
      <span className="w-px bg-white sellzio-bar-3"></span>
      <span className="w-px bg-white sellzio-bar-4"></span>
    </span>
    LIVE
  </div>
)

// Live Corner Badge - sesuai dengan Velozio (di pojok kiri atas)
export const SellzioLiveCornerBadge = () => (
  <div className="absolute top-0 left-0 bg-[#ee4d2d] text-white text-[10px] font-bold px-1 py-0.5 rounded-br-[3px] z-10 flex items-center">
    <span className="w-2 h-2 bg-white rounded-full mr-0.5 animate-pulse"></span>
    LIVE
  </div>
)

// Termurah di Toko Badge - sesuai dengan Velozio
export const SellzioTermurahDiTokoBadge = () => (
  <div className="relative inline-flex items-center bg-[#ee4d2d] text-white text-[10px] font-semibold px-2 py-0.5 pl-5 rounded-[3px] mb-1.5 shadow-sm ml-3">
    <span className="absolute -left-2.5 top-1/2 -translate-y-1/2 bg-white text-[#ee4d2d] w-6 h-6 rounded-full flex items-center justify-center border border-[#ee4d2d] shadow-sm">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
        <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z" />
      </svg>
    </span>
    Termurah di Toko
  </div>
)

// Komisi Xtra Badge - sesuai dengan Velozio
export const SellzioKomisiXtraBadge = () => (
  <div className="inline-flex items-center bg-white border border-[#ee4d2d] rounded-sm py-0.5 px-1.5 text-[10px] font-semibold mb-1.5">
    <span className="text-[#ee4d2d]">KOMISI</span>
    <span className="text-[#005bac] ml-0.5">XTRA</span>
  </div>
)

// CSS Styles - untuk animasi gelombang suara
export const SellzioBadgeStyles = () => (
  <style jsx global>{`
    .sellzio-bar-1 {
      height: 1px;
      animation: sellzio-equalizer-1 1.5s infinite;
    }

    .sellzio-bar-2 {
      height: 3px;
      animation: sellzio-equalizer-2 1.5s infinite;
    }

    .sellzio-bar-3 {
      height: 2px;
      animation: sellzio-equalizer-3 1.5s infinite;
    }

    .sellzio-bar-4 {
      height: 1px;
      animation: sellzio-equalizer-1 1.5s infinite;
    }

    @keyframes sellzio-equalizer-1 {
      0%, 100% { height: 1px; }
      50% { height: 3px; }
    }

    @keyframes sellzio-equalizer-2 {
      0%, 100% { height: 3px; }
      50% { height: 1px; }
    }

    @keyframes sellzio-equalizer-3 {
      0%, 100% { height: 2px; }
      50% { height: 4px; }
    }
  `}</style>
)
