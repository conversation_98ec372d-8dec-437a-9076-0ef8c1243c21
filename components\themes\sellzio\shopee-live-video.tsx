"use client"

import React, { useState } from 'react'

interface VideoData {
  id: string
  title: string
  thumbnail: string
  isLive?: boolean
  viewCount?: string
  type: 'live' | 'video'
}

interface ShopeeLiveVideoProps {
  className?: string
}

const ShopeeLiveVideo: React.FC<ShopeeLiveVideoProps> = ({ className = "" }) => {
  const [activeVideo, setActiveVideo] = useState<string | null>(null)

  // Sample data untuk live dan video
  const liveVideos: VideoData[] = [
    {
      id: 'live1',
      title: 'Flash Sale Elektronik 50% Off! Smartphone Gaming Murah',
      thumbnail: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=200&h=300&fit=crop',
      isLive: true,
      viewCount: '2.1K',
      type: 'live'
    },
    {
      id: 'live2',
      title: 'Live Shopping Fashion Terbaru - Dress Korea Style',
      thumbnail: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=300&fit=crop',
      isLive: true,
      viewCount: '856',
      type: 'live'
    }
  ]

  const shortVideos: VideoData[] = [
    {
      id: 'video1',
      title: 'Review Gadget Terbaru 2024 - iPhone vs Samsung',
      thumbnail: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=200&h=300&fit=crop',
      viewCount: '15.2K',
      type: 'video'
    },
    {
      id: 'video2',
      title: 'Tips Belanja Hemat di Shopee - Cara Dapat Voucher',
      thumbnail: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=200&h=300&fit=crop',
      viewCount: '8.7K',
      type: 'video'
    }
  ]

  const handleVideoClick = (videoId: string) => {
    setActiveVideo(videoId)
    // Implementasi untuk membuka video/live stream
    console.log('Opening video:', videoId)
  }

  const renderVideoCard = (video: VideoData) => (
    <div
      key={video.id}
      className="video-card"
      onClick={() => handleVideoClick(video.id)}
    >
      <div
        className="video-thumbnail"
        style={{ backgroundImage: `url(${video.thumbnail})` }}
      >
        {video.isLive ? (
          <div className="label">
            <div className="dot"></div>
            LIVE
          </div>
        ) : (
          <div className="view-count">
            <div className="view-icon"></div>
            {video.viewCount}
          </div>
        )}
        
        <div className="video-title">
          {video.title}
        </div>

        {!video.isLive && (
          <div className="play-icon">
            <i className="fas fa-play"></i>
          </div>
        )}
      </div>
    </div>
  )

  return (
    <div className={`container1 ${className}`}>
      {/* Shopee Live Section */}
      <div className="section">
        <div className="section-header">
          <div className="section-title">
            Shopee Live
            <span className="title-icon">📺</span>
          </div>
        </div>
        <div className="videos-grid">
          {liveVideos.map(renderVideoCard)}
        </div>
      </div>

      {/* Video Section */}
      <div className="section">
        <div className="section-header">
          <div className="section-title">
            Video
            <span className="title-icon">🎬</span>
          </div>
        </div>
        <div className="videos-grid">
          {shortVideos.map(renderVideoCard)}
        </div>
      </div>
    </div>
  )
}

export default ShopeeLiveVideo
